const API_BASE_URL = "http://localhost:5000/api"

class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    // For demo purposes, return mock data instead of making real API calls
    return this.getMockResponse(endpoint, options) as T
  }

  private getMockResponse(endpoint: string, options?: RequestInit) {
    // Mock responses for different endpoints
    if (endpoint === "/company") {
      return [
        {
          id: "1",
          companyName: "TechFlow Solutions",
          servicesOffered: "Web Development, Mobile Apps, Cloud Solutions, AI Integration",
          serviceOverview: "We help businesses transform digitally with cutting-edge technology solutions",
          aboutTheCompany:
            "A leading technology consultancy with 10+ years of experience in delivering innovative solutions for startups and enterprises.",
        },
        {
          id: "2",
          companyName: "GreenEarth Marketing",
          servicesOffered: "Digital Marketing, SEO, Content Strategy, Social Media Management",
          serviceOverview: "Sustainable marketing strategies that grow your business while protecting the environment",
          aboutTheCompany:
            "An eco-friendly marketing agency focused on helping environmentally conscious brands reach their target audience.",
        },
      ]
    }

    if (endpoint === "/blog/drafts") {
      return [
        {
          id: "draft-1",
          companyName: "TechFlow Solutions",
          selectedKeyword: "AI automation tools",
          currentStep: 3,
          status: "draft",
          lastEdited: "2024-01-15T10:30:00Z",
        },
      ]
    }

    if (endpoint === "/blog/start") {
      return { draftId: "new-draft-" + Date.now() }
    }

    if (endpoint === "/blog/select-keyword-analyze") {
      return {
        analysis: {
          competitors: [
            { domain: "zapier.com", title: "Automation Guide", domainAuthority: 94, wordCount: 3200, seoScore: 92 },
          ],
          cluster: [{ keyword: "business automation", searchVolume: 8100, difficulty: 65, relevanceScore: 95 }],
          trends: [{ topic: "AI Automation", description: "Growing trend", direction: "up", confidence: 89 }],
        },
      }
    }

    if (endpoint === "/blog/generate-meta-scores") {
      return {
        metaOptions: [
          {
            h1Title: "Complete Guide to AI Automation Tools",
            metaTitle: "AI Automation Tools Guide 2024",
            metaDescription: "Discover the best AI automation tools for your business",
            scores: { keywordScore: 95, lengthScore: 88, readabilityScore: 92, trendScore: 89, totalScore: 91 },
            keywordsIncluded: ["AI automation", "tools", "business"],
          },
        ],
      }
    }

    if (endpoint === "/blog/generate-structured-content") {
      return {
        blocks: [
          {
            id: "intro-1",
            type: "introduction",
            content: "This is the introduction to your blog post about AI automation tools...",
            editable: true,
            wordCount: 124,
          },
        ],
      }
    }

    if (endpoint === "/blog/generate-links") {
      return {
        internalLinks: [
          { anchorText: "automation guide", targetUrl: "/blog/automation", context: "Related content", relevance: 92 },
        ],
        externalLinks: [{ anchorText: "Zapier", targetDomain: "zapier.com", context: "Tool reference", relevance: 95 }],
      }
    }

    if (endpoint === "/blog/deploy-wordpress") {
      return { success: true, message: "Deployed successfully" }
    }

    if (endpoint === "/blog/test-wordpress") {
      return { connected: true }
    }

    if (endpoint.startsWith("/blog/draft/")) {
      return {
        keywords: [
          {
            focusKeyword: "AI automation tools for small business",
            articleFormat: "How-to Guide",
            wordCount: "2,500-3,000",
            targetAudience: "Small Business Owners",
            objective: "Drive lead generation and establish thought leadership",
            source: "ai",
          },
        ],
        blocks: [
          {
            id: "intro-1",
            type: "introduction",
            content: "Introduction content here...",
            editable: true,
            wordCount: 124,
          },
        ],
        internalLinks: [],
        externalLinks: [],
      }
    }

    // Default response
    return { success: true }
  }

  // Keep all the existing method signatures but they now use mock data
  async getCompanies() {
    return this.request("/company")
  }

  async startBlog(companyName: string) {
    return this.request("/blog/start", {
      method: "POST",
      body: JSON.stringify({ companyName }),
    })
  }

  async selectKeywordAnalyze(draftId: string, selectedKeyword: string) {
    return this.request("/blog/select-keyword-analyze", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedKeyword }),
    })
  }

  async generateMetaScores(draftId: string) {
    return this.request("/blog/generate-meta-scores", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async selectMeta(draftId: string, selectedMetaIndex: number) {
    return this.request("/blog/select-meta", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedMetaIndex }),
    })
  }

  async generateStructuredContent(draftId: string) {
    return this.request("/blog/generate-structured-content", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async regenerateBlock(
    draftId: string,
    blockId: string,
    regenerationType: "ai" | "manual",
    customPrompt?: string,
    newContent?: string,
  ) {
    return this.request("/blog/regenerate-block", {
      method: "POST",
      body: JSON.stringify({ draftId, blockId, regenerationType, customPrompt, newContent }),
    })
  }

  async generateLinks(draftId: string) {
    return this.request("/blog/generate-links", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async deployWordPress(draftId: string) {
    return this.request("/blog/deploy-wordpress", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async getDraft(draftId: string) {
    return this.request(`/blog/draft/${draftId}`)
  }

  async listDrafts() {
    return this.request("/blog/drafts")
  }

  async testWordPress() {
    return this.request("/blog/test-wordpress")
  }
}

export const api = new ApiClient()
