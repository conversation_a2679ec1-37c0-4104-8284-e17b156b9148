"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { RefreshCw, Eye, Tag } from "lucide-react"
import { StepperHeader } from "@/components/stepper-header"
import { SEOScoreCircle } from "@/components/seo-score-circle"

interface MetaBlock {
  id: string
  type: "h1" | "metaTitle" | "metaDescription"
  content: string
  scores: {
    keywordScore: number
    lengthScore: number
    readabilityScore: number
    trendScore: number
    totalScore: number
  }
  keywordsIncluded: string[]
}

export default function MetaPage() {
  const [metaBlocks, setMetaBlocks] = useState<MetaBlock[]>([])
  const [selectedBlocks, setSelectedBlocks] = useState<{
    h1: string
    metaTitle: string
    metaDescription: string
  }>({
    h1: "",
    metaTitle: "",
    metaDescription: "",
  })
  const [loading, setLoading] = useState(true)
  const [regenerating, setRegenerating] = useState<string>("")
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()

  const draftId = params.draftId as string

  useEffect(() => {
    generateMetaBlocks()
  }, [])

  const generateMetaBlocks = async () => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Generate 9 separate blocks: 3 H1, 3 Meta Title, 3 Meta Description
      const mockMetaBlocks: MetaBlock[] = [
        // H1 Titles
        {
          id: "h1-1",
          type: "h1",
          content: "The Complete Guide to AI Automation Tools for Small Business Success in 2024",
          scores: { keywordScore: 95, lengthScore: 88, readabilityScore: 92, trendScore: 89, totalScore: 91 },
          keywordsIncluded: ["AI automation tools", "small business", "2024", "success"],
        },
        {
          id: "h1-2",
          type: "h1",
          content: "How Small Businesses Can Leverage AI Automation Tools to Scale Operations",
          scores: { keywordScore: 87, lengthScore: 92, readabilityScore: 89, trendScore: 94, totalScore: 90 },
          keywordsIncluded: ["AI automation", "small business", "scale operations"],
        },
        {
          id: "h1-3",
          type: "h1",
          content: "Top 15 AI Automation Tools Every Small Business Owner Should Know About",
          scores: { keywordScore: 92, lengthScore: 85, readabilityScore: 88, trendScore: 87, totalScore: 88 },
          keywordsIncluded: ["AI automation tools", "small business owner", "top tools"],
        },
        // Meta Titles
        {
          id: "meta-title-1",
          type: "metaTitle",
          content: "AI Automation Tools for Small Business: Complete 2024 Guide | TechFlow",
          scores: { keywordScore: 93, lengthScore: 90, readabilityScore: 85, trendScore: 88, totalScore: 89 },
          keywordsIncluded: ["AI automation tools", "small business", "2024 guide"],
        },
        {
          id: "meta-title-2",
          type: "metaTitle",
          content: "Scale Your Small Business with AI Automation Tools - Expert Guide",
          scores: { keywordScore: 88, lengthScore: 95, readabilityScore: 90, trendScore: 92, totalScore: 91 },
          keywordsIncluded: ["small business", "AI automation tools", "expert guide"],
        },
        {
          id: "meta-title-3",
          type: "metaTitle",
          content: "15 Best AI Automation Tools for Small Business Owners (2024 Review)",
          scores: { keywordScore: 90, lengthScore: 87, readabilityScore: 92, trendScore: 85, totalScore: 88 },
          keywordsIncluded: ["AI automation tools", "small business owners", "2024"],
        },
        // Meta Descriptions
        {
          id: "meta-desc-1",
          type: "metaDescription",
          content:
            "Discover the best AI automation tools for small businesses. Learn how to streamline operations, reduce costs, and boost productivity with our comprehensive guide.",
          scores: { keywordScore: 89, lengthScore: 92, readabilityScore: 94, trendScore: 86, totalScore: 90 },
          keywordsIncluded: ["AI automation tools", "small businesses", "streamline operations"],
        },
        {
          id: "meta-desc-2",
          type: "metaDescription",
          content:
            "Transform your small business operations with AI automation. Get expert insights on the top tools, implementation strategies, and ROI optimization techniques.",
          scores: { keywordScore: 91, lengthScore: 88, readabilityScore: 87, trendScore: 93, totalScore: 89 },
          keywordsIncluded: ["small business", "AI automation", "expert insights"],
        },
        {
          id: "meta-desc-3",
          type: "metaDescription",
          content:
            "Compare the top 15 AI automation tools for small businesses. Features, pricing, pros & cons, and real user reviews to help you choose the right solution.",
          scores: { keywordScore: 87, lengthScore: 90, readabilityScore: 91, trendScore: 88, totalScore: 89 },
          keywordsIncluded: ["AI automation tools", "small businesses", "compare", "reviews"],
        },
      ]

      setMetaBlocks(mockMetaBlocks)
    } catch (error) {
      toast({
        title: "Error generating meta blocks",
        description: "Failed to generate SEO meta blocks. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRegenerate = async (blockId: string) => {
    setRegenerating(blockId)
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000))
      toast({
        title: "Block regenerated",
        description: "Meta block has been regenerated successfully.",
      })
    } catch (error) {
      toast({
        title: "Regeneration failed",
        description: "Failed to regenerate meta block. Please try again.",
        variant: "destructive",
      })
    } finally {
      setRegenerating("")
    }
  }

  const handleBlockSelect = (blockId: string, type: string) => {
    setSelectedBlocks((prev) => ({
      ...prev,
      [type]: blockId,
    }))
  }

  const handleContinue = async () => {
    if (!selectedBlocks.h1 || !selectedBlocks.metaTitle || !selectedBlocks.metaDescription) {
      toast({
        title: "Please select all meta elements",
        description: "You must select one H1 title, one meta title, and one meta description.",
        variant: "destructive",
      })
      return
    }

    try {
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Save selected meta data
      const selectedMeta = {
        h1Title: metaBlocks.find((b) => b.id === selectedBlocks.h1)?.content || "",
        metaTitle: metaBlocks.find((b) => b.id === selectedBlocks.metaTitle)?.content || "",
        metaDescription: metaBlocks.find((b) => b.id === selectedBlocks.metaDescription)?.content || "",
      }

      localStorage.setItem(`meta_${draftId}`, JSON.stringify(selectedMeta))

      toast({
        title: "Meta information saved",
        description: "SEO meta information has been saved successfully.",
      })

      router.push(`/blog/${draftId}/editor`)
    } catch (error) {
      toast({
        title: "Error saving meta",
        description: "Failed to save meta selection. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getBlockTitle = (type: string) => {
    switch (type) {
      case "h1":
        return "H1 Title"
      case "metaTitle":
        return "Meta Title"
      case "metaDescription":
        return "Meta Description"
      default:
        return "Meta Block"
    }
  }

  const renderMetaSection = (type: "h1" | "metaTitle" | "metaDescription") => {
    const blocks = metaBlocks.filter((block) => block.type === type)
    const selectedId =
      selectedBlocks[type === "metaTitle" ? "metaTitle" : type === "metaDescription" ? "metaDescription" : "h1"]

    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">{getBlockTitle(type)}s</h2>
        <RadioGroup
          value={selectedId}
          onValueChange={(value) =>
            handleBlockSelect(
              value,
              type === "metaTitle" ? "metaTitle" : type === "metaDescription" ? "metaDescription" : "h1",
            )
          }
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {blocks.map((block) => (
              <div key={block.id} className="relative">
                <Label htmlFor={block.id} className="cursor-pointer">
                  <Card
                    className={`hover:shadow-lg transition-all duration-200 ${
                      selectedId === block.id ? "ring-2 ring-[#0066cc] border-[#0066cc]" : "hover:border-gray-300"
                    }`}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <SEOScoreCircle score={block.scores.totalScore} size="md" />
                            <RadioGroupItem value={block.id} id={block.id} />
                          </div>
                          <CardTitle className="text-base leading-tight">{block.content}</CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Score Breakdown</h4>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="flex justify-between">
                            <span>Keywords:</span>
                            <span className={getScoreColor(block.scores.keywordScore)}>
                              {block.scores.keywordScore}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Length:</span>
                            <span className={getScoreColor(block.scores.lengthScore)}>{block.scores.lengthScore}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Readability:</span>
                            <span className={getScoreColor(block.scores.readabilityScore)}>
                              {block.scores.readabilityScore}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Trends:</span>
                            <span className={getScoreColor(block.scores.trendScore)}>{block.scores.trendScore}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Keywords Included</h4>
                        <div className="flex flex-wrap gap-1">
                          {block.keywordsIncluded.map((keyword, kidx) => (
                            <Badge key={kidx} variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          onClick={() => handleRegenerate(block.id)}
                          disabled={regenerating === block.id}
                          variant="outline"
                          size="sm"
                          className="flex-1"
                        >
                          <RefreshCw className={`h-4 w-4 mr-1 ${regenerating === block.id ? "animate-spin" : ""}`} />
                          {regenerating === block.id ? "Regenerating..." : "Regenerate"}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </Label>
              </div>
            ))}
          </div>
        </RadioGroup>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={2} draftId={draftId} />
        <main className="max-w-7xl mx-auto px-6 py-8">
          <div className="space-y-8">
            <Skeleton className="h-8 w-64" />
            {[1, 2, 3].map((section) => (
              <div key={section} className="space-y-4">
                <Skeleton className="h-6 w-32" />
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-80" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={2} draftId={draftId} />

      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="space-y-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">SEO Meta Generation</h1>
            <p className="text-gray-600">Choose the best SEO-optimized meta information for your blog post</p>
          </div>

          {/* H1 Titles Section */}
          {renderMetaSection("h1")}

          {/* Meta Titles Section */}
          {renderMetaSection("metaTitle")}

          {/* Meta Descriptions Section */}
          {renderMetaSection("metaDescription")}

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => router.back()}>
              Back to Keywords
            </Button>

            <Button
              onClick={handleContinue}
              disabled={!selectedBlocks.h1 || !selectedBlocks.metaTitle || !selectedBlocks.metaDescription}
              className="bg-[#0066cc] hover:bg-blue-700"
            >
              Continue to Content
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
